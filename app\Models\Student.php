<?php

namespace App\Models;

use App\Enums\GenderEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Student extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'nis',
        'nisn',
        'birth_place',
        'birth_date',
        'gender',
        'religion',
        'address',
        'phone',
        'parent_name',
        'parent_phone',
        'parent_occupation',
        'parent_address',
        'entry_year',
        'profile_picture',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birth_date' => 'date',
        'entry_year' => 'integer',
        'gender' => GenderEnum::class,
    ];

    /**
     * Get the user that owns the student.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The classrooms that belong to the student.
     */
    public function classrooms(): BelongsToMany
    {
        return $this->belongsToMany(Classroom::class, 'classroom_students')
            ->withPivot('academic_year_id')
            ->using(ClassroomStudent::class)
            ->withTimestamps();
    }

    /**
     * Get the student's current classroom (in the active academic year).
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function getCurrentClassroomAttribute()
    {
        $activeAcademicYear = AcademicYear::where('status', 'active')->first();

        if (!$activeAcademicYear) {
            return null;
        }

        return $this->classrooms()
            ->wherePivot('academic_year_id', $activeAcademicYear->id)
            ->with('academicYear')
            ->first();
    }

    /**
     * Get the student's classroom enrollments.
     */
    public function classroomStudents(): HasMany
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    /**
     * Get the student's attendances.
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the full name from the associated user.
     */
    public function getFullNameAttribute(): string
    {
        return $this->user->name ?? '';
    }

    /**
     * Get the email from the associated user.
     */
    public function getEmailAttribute(): string
    {
        return $this->user->email ?? '';
    }

    /**
     * Get the username from the associated user.
     */
    public function getUsernameAttribute(): string
    {
        return $this->user->username ?? '';
    }

    /**
     * Get the status from the associated user.
     */
    public function getStatusAttribute(): bool
    {
        return $this->user->status ?? false;
    }

    /**
     * Get the profile picture URL.
     */
    public function getProfilePictureUrlAttribute(): string
    {
        if ($this->profile_picture) {
            return asset("storage/{$this->profile_picture}");
        }

        return asset('assets/images/default-student-avatar.png');
    }

    /**
     * Check if the student is currently enrolled in any classroom.
     */
    public function isEnrolled(): bool
    {
        $activeAcademicYear = AcademicYear::where('status', 'active')->first();

        if (!$activeAcademicYear) {
            return false;
        }

        return $this->classrooms()
            ->wherePivot('academic_year_id', $activeAcademicYear->id)
            ->exists();
    }
}
