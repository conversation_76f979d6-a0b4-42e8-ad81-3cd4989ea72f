<?php $__env->startSection('title', 'Guru'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Guru',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            Daftar <?php echo $__env->yieldContent('title'); ?>
                            <span class="badge bg-primary-subtle text-primary ms-2" id="total-teachers">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" data-action="export">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" data-action="import">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="<?php echo e(route('admin.teachers.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-3">
                            <label for="filter-status" class="form-label">Status</label>
                            <select class="form-select" data-choices name="status" id="filter-status">
                                <option value="">Semua Status</option>
                                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter-role" class="form-label">Role</label>
                            <select class="form-select" data-choices name="role" id="filter-role">
                                <option value="">Semua Role</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter-gender" class="form-label">Jenis Kelamin</label>
                            <select class="form-select" data-choices name="gender" id="filter-gender">
                                <option value="">Semua Jenis Kelamin</option>
                                <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search-input" class="form-label">Cari</label>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Cari nama, email, tempat lahir..." id="search-input" name="search">
                                <button class="btn btn-primary" type="button" id="search-button">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table id="teachers-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Tanggal Lahir</th>
                                    <th>Nomor Telp</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .dataTables_length,
        .dataTables_filter {
            display: none;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.datatables', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const table = new DataTable('#teachers-table', {
                processing: true,
                serverSide: true,
                ajax: {
                    url: '<?php echo e(route('admin.teachers.index')); ?>',
                    data: (d) => ({
                        ...d,
                        status: document.querySelector('#filter-status').value,
                        role: document.querySelector('#filter-role').value,
                        gender: document.querySelector('#filter-gender').value,
                        search: document.querySelector('#search-input').value
                    }),
                    complete: (response) => {
                        document.querySelector('#total-teachers').textContent = response.responseJSON?.recordsTotal ?? 0;
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'user.name',
                        name: 'user.name'
                    },
                    {
                        data: 'user.email',
                        name: 'user.email'
                    },
                    {
                        data: 'user.role',
                        name: 'user.role'
                    },
                    {
                        data: 'gender',
                        name: 'gender'
                    },
                    {
                        data: 'birth_date',
                        name: 'birth_date'
                    },
                    {
                        data: 'phone_number',
                        name: 'phone_number'
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: 'Cari...',
                    lengthMenu: 'Tampilkan _MENU_ data',
                    zeroRecords: 'Data tidak ditemukan',
                    info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                    infoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                    infoFiltered: '(disaring dari _MAX_ total data)',
                    paginate: {
                        first: 'Pertama',
                        last: 'Terakhir',
                        next: 'Selanjutnya',
                        previous: 'Sebelumnya'
                    }
                },
                order: [
                    [1, 'asc']
                ]
            });

            // Adjust table columns on sidebar toggle
            document.querySelector('#topnav-hamburger-icon')?.addEventListener('click', () => {
                setTimeout(() => table.columns.adjust().draw(), 300);
            });

            // Handle filter changes
            ['#filter-status', '#filter-role', '#filter-gender'].forEach(selector => {
                document.querySelector(selector).addEventListener('change', () => table.draw());
            });

            // Handle search input and button
            document.querySelector('#search-input').addEventListener('keyup', (e) => {
                if (e.key === 'Enter') table.draw();
            });
            document.querySelector('#search-button').addEventListener('click', () => table.draw());

            // Handle export/import buttons
            document.querySelectorAll('[data-action="export"], [data-action="import"]').forEach(button => {
                button.addEventListener('click', () => {
                    Swal.fire({
                        title: 'Coming Soon!',
                        text: `Fitur ${button.dataset.action} akan segera tersedia.`,
                        icon: 'info'
                    });
                });
            });

            // Event delegation for action buttons
            document.querySelector('#teachers-table').addEventListener('click', (e) => {
                const deleteBtn = e.target.closest('[data-action="delete"]');
                const toggleBtn = e.target.closest('[data-action="toggle-status"]');
                if (deleteBtn) deleteTeacher(deleteBtn);
                if (toggleBtn) toggleTeacherStatus(toggleBtn);
            });
        });

        async function deleteTeacher(element) {
            try {
                const url = element.dataset.url;
                const {
                    isConfirmed
                } = await Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus data guru ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true
                });

                if (!isConfirmed) return;

                await Swal.fire({
                    title: 'Memproses...',
                    text: 'Sedang menghapus data guru',
                    allowOutsideClick: false,
                    didOpen: () => Swal.showLoading()
                });

                const response = await fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                if (!response.ok) throw new Error(data.message || 'Gagal menghapus data guru');

                await Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });

                document.querySelector('#teachers-table').DataTable().ajax.reload(null, false);
            } catch (error) {
                await Swal.fire({
                    title: 'Gagal Menghapus',
                    text: error.message || 'Terjadi kesalahan saat menghapus data',
                    icon: 'error'
                });
            }
        }

        async function toggleTeacherStatus(element) {
            try {
                const url = element.dataset.url;
                const id = element.dataset.id;
                const status = element.checked;
                const actionText = status ? 'mengaktifkan' : 'menonaktifkan';

                const {
                    isConfirmed
                } = await Swal.fire({
                    title: 'Konfirmasi Status',
                    text: `Anda yakin ingin ${actionText} data guru ini?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: `Ya, ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
                    cancelButtonText: 'Batal',
                    reverseButtons: true
                });

                if (!isConfirmed) {
                    element.checked = !status;
                    return;
                }

                await Swal.fire({
                    title: 'Memproses...',
                    text: `Sedang ${actionText} data guru`,
                    allowOutsideClick: false,
                    didOpen: () => Swal.showLoading()
                });

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id,
                        status
                    })
                });

                const data = await response.json();
                if (!response.ok) throw new Error(data.message || `Gagal ${actionText} data guru`);

                await Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });

                document.querySelector('#teachers-table').DataTable().ajax.reload(null, false);
            } catch (error) {
                await Swal.fire({
                    title: `Gagal ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
                    text: error.message || 'Terjadi kesalahan saat mengubah status',
                    icon: 'error'
                });
                element.checked = !status;
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/teacher/index.blade.php ENDPATH**/ ?>