@extends('admin.layouts.app')

@section('title', 'Edit Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Guru',
        'breadcrumb' => 'Manajemen Akun',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0">Form Data Guru</h5>
                        </div>
                        <div>
                            <a href="{{ route('admin.teachers.index') }}" class="btn btn-ghost-info">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="editTeacherForm" action="{{ route('admin.teachers.update', $teacher->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Personal Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Pribadi</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Name -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ old('name', $teacher->user->name) }}"
                                                   placeholder="Masukkan nama lengkap" required>
                                            <div class="invalid-feedback" data-field="name"></div>
                                        </div>
                                    </div>

                                    <!-- Gender -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                            <div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-male" value="male" required
                                                           {{ old('gender', $teacher->gender?->value) === 'male' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gender-male">Laki-laki</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-female" value="female" required
                                                           {{ old('gender', $teacher->gender?->value) === 'female' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gender-female">Perempuan</label>
                                                </div>
                                            </div>
                                            <div class="invalid-feedback" data-field="gender"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Place -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="birth_place" name="birth_place"
                                                   value="{{ old('birth_place', $teacher->birth_place) }}"
                                                   placeholder="Masukkan tempat lahir" required>
                                            <div class="invalid-feedback" data-field="birth_place"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Date -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control flatpickr-input" data-provider="flatpickr"
                                                   data-date-format="Y-m-d" id="birth_date" name="birth_date"
                                                   value="{{ old('birth_date', $teacher->birth_date?->format('Y-m-d')) }}"
                                                   placeholder="Masukkan tanggal lahir" readonly="readonly" required>
                                            <div class="invalid-feedback" data-field="birth_date"></div>
                                        </div>
                                    </div>

                                    <!-- Phone -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="phone_number" class="form-label">Nomor Telepon</label>
                                            <input type="text" class="form-control" id="phone_number" name="phone_number"
                                                   value="{{ old('phone_number', $teacher->phone_number) }}"
                                                   placeholder="Masukkan nomor telepon">
                                            <div class="invalid-feedback" data-field="phone_number"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Akun</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Email -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="{{ old('email', $teacher->user->email) }}"
                                                   placeholder="<EMAIL>" required>
                                            <div class="invalid-feedback" data-field="email"></div>
                                        </div>
                                    </div>

                                    <!-- Username -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="username" name="username"
                                                   value="{{ old('username', $teacher->user->username) }}"
                                                   placeholder="Masukkan username" required>
                                            <div class="invalid-feedback" data-field="username"></div>
                                        </div>
                                    </div>

                                    <!-- Password -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="password" class="form-label">Password</label>
                                            <div class="position-relative">
                                                <input type="password" class="form-control" id="password" name="password">
                                                <button type="button" class="position-absolute top-50 end-0 translate-middle-y btn btn-sm"
                                                        id="password-toggle" style="margin-right: 10px;">
                                                    <i class="ri-eye-off-line"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Kosongkan jika tidak ingin mengubah password. Minimal 8 karakter.</small>
                                            <div class="invalid-feedback" data-field="password"></div>
                                        </div>
                                    </div>

                                    <!-- Role -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="role" name="role" required>
                                                <option value="">Pilih Role</option>
                                                @foreach ($roles as $value => $label)
                                                    @if (str_contains($value, 'teacher'))
                                                        <option value="{{ $value }}" {{ old('role', $currentRole) === $value ? 'selected' : '' }}>
                                                            {{ $label }}
                                                        </option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="role"></div>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                @foreach ($statuses as $value => $label)
                                                    <option value="{{ $value }}" {{ old('status', $teacher->user->status->value) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="ri-save-line align-bottom me-1"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Toggle password visibility
            $('#password-toggle').on('click', function() {
                const passwordInput = $('#password');
                const icon = $(this).find('i');

                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    icon.removeClass('ri-eye-off-line').addClass('ri-eye-line');
                } else {
                    passwordInput.attr('type', 'password');
                    icon.removeClass('ri-eye-line').addClass('ri-eye-off-line');
                }
            });

            flatpickr("[data-provider='flatpickr']", {
                dateFormat: "Y-m-d",
                maxDate: "today", // Prevents future dates
                disableMobile: "true",
                allowInput: true,
                locale: {
                    firstDayOfWeek: 1,
                    weekdays: {
                        shorthand: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
                        longhand: ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
                    },
                    months: {
                        shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                        longhand: ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember']
                    }
                }
            });

            // Form submission
            $('#editTeacherForm').on('submit', function(e) {
                e.preventDefault();

                // Reset previous validation errors
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                // Show loading state
                const submitBtn = $('#submit-btn');
                const originalBtnText = submitBtn.html();
                submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
                submitBtn.prop('disabled', true);

                // Prepare form data
                const formData = new FormData(this);

                // Submit form via AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                window.location.href = "{{ route('admin.teachers.index') }}";
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            // Validation errors
                            const errors = xhr.responseJSON.errors;

                            // Display validation errors
                            $.each(errors, function(field, messages) {
                                const inputField = $(`[name="${field}"]`);
                                inputField.addClass('is-invalid');

                                const errorField = $(`[data-field="${field}"]`);
                                errorField.text(messages[0]);
                            });
                        } else {
                            // General error
                            Swal.fire({
                                title: 'Error!',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat memperbarui data',
                                icon: 'error'
                            });
                        }
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.html(originalBtnText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush
