<?php $__env->startSection('title', 'Edit Staf'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Edit Staf',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Formulir Edit Staf</h5>
                </div>
                <div class="card-body">
                    <form id="edit-staff-form" action="<?php echo e(route('admin.staff.update', $staff->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name', $staff->name)); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" value="<?php echo e(old('username', $staff->username)); ?>" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email', $staff->email)); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="password" class="form-label">Password <small class="text-muted">(Kosongkan jika tidak ingin mengubah)</small></label>
                                <input type="password" class="form-control" id="password" name="password">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="role" name="role" required>
                                    <option value="">Pilih Role</option>
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>" <?php echo e(old('role', $currentRole) == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="status" name="status" required>
                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>" <?php echo e(old('status', $staff->status->value) == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line align-bottom me-1"></i> Perbarui
                            </button>
                            <a href="<?php echo e(route('admin.staff.index')); ?>" class="btn btn-light">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Perhatian:</h6>
                        <ul class="mb-0">
                            <li>Kosongkan password jika tidak ingin mengubah</li>
                            <li>Email harus unik dalam sistem</li>
                            <li>Perubahan role akan mempengaruhi akses</li>
                            <li>Anda tidak dapat mengubah akun sendiri</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h6>Informasi Akun:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>Bergabung:</strong><br>
                                <small class="text-muted"><?php echo e($staff->created_at->format('d F Y H:i')); ?></small>
                            </li>
                            <li class="mb-2">
                                <strong>Terakhir Update:</strong><br>
                                <small class="text-muted"><?php echo e($staff->updated_at->format('d F Y H:i')); ?></small>
                            </li>
                            <li class="mb-2">
                                <strong>Role Saat Ini:</strong><br>
                                <span class="badge bg-primary-subtle text-primary">
                                    <?php if(is_array($roles) && is_string($currentRole) && array_key_exists($currentRole, $roles)): ?>
                                        <?php echo e($roles[$currentRole]); ?>

                                    <?php else: ?>
                                        <?php echo e($currentRole ?? 'Tidak Ada'); ?>

                                    <?php endif; ?>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Handle form submission
            $('#edit-staff-form').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const formData = new FormData(form[0]);

                // Show loading
                Swal.fire({
                    title: 'Memproses...',
                    text: 'Sedang memperbarui data staf',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Submit form
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                window.location.href = "<?php echo e(route('admin.staff.index')); ?>";
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Terjadi kesalahan saat memperbarui data';

                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = xhr.responseJSON.errors;
                            errorMessage = Object.values(errors).flat().join('<br>');
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Gagal',
                            html: errorMessage,
                            icon: 'error'
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/staff/edit.blade.php ENDPATH**/ ?>