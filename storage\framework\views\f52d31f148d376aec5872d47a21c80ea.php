<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['viewUrl', 'editUrl', 'enrollUrl', 'deleteUrl', 'id']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['viewUrl', 'editUrl', 'enrollUrl', 'deleteUrl', 'id']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="dropdown">
    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="ri-more-fill"></i>
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="<?php echo e($viewUrl); ?>"><i class="ri-eye-fill text-primary me-2"></i>Lihat</a></li>
        <li><a class="dropdown-item" href="<?php echo e($editUrl); ?>"><i class="ri-pencil-fill text-warning me-2"></i>Edit</a></li>
        <li><a class="dropdown-item enroll-btn" href="javascript:void(0);" data-id="<?php echo e($id); ?>"><i class="ri-graduation-cap-fill text-success me-2"></i>Daftarkan ke Kelas</a></li>
        <li>
            <hr class="dropdown-divider">
        </li>
        <li><a class="dropdown-item delete-btn" href="javascript:void(0);" data-id="<?php echo e($id); ?>"><i class="ri-delete-bin-fill text-danger me-2"></i>Hapus</a></li>
    </ul>
</div>
<?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/components/student-actions.blade.php ENDPATH**/ ?>