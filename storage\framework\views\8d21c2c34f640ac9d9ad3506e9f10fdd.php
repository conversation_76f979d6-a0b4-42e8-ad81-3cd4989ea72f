<?php $__env->startSection('title', 'Tambah Siswa'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Tambah Siswa Baru',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0">Form Data Siswa</h5>
                        </div>
                        <div>
                            <a href="<?php echo e(route('admin.students.index')); ?>" class="btn btn-ghost-info">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form id="createStudentForm" action="<?php echo e(route('admin.students.store')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <!-- Personal Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Pribadi</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Name -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   placeholder="Masukkan nama lengkap" required>
                                            <div class="invalid-feedback" data-field="name"></div>
                                        </div>
                                    </div>

                                    <!-- Gender -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                            <div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-male" value="male" required checked>
                                                    <label class="form-check-label" for="gender-male">Laki-laki</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-female" value="female" required>
                                                    <label class="form-check-label" for="gender-female">Perempuan</label>
                                                </div>
                                            </div>
                                            <div class="invalid-feedback" data-field="gender"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Place -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="birth_place" name="birth_place"
                                                   placeholder="Masukkan tempat lahir" required>
                                            <div class="invalid-feedback" data-field="birth_place"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Date -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control flatpickr-input" data-provider="flatpickr"
                                                   data-date-format="Y-m-d" id="birth_date" name="birth_date"
                                                   placeholder="Masukkan tanggal lahir" readonly="readonly" required>
                                            <div class="invalid-feedback" data-field="birth_date"></div>
                                        </div>
                                    </div>

                                    <!-- Student ID Numbers -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="nis" class="form-label">NIS <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="nis" name="nis"
                                                   placeholder="Masukkan NIS" required>
                                            <div class="invalid-feedback" data-field="nis"></div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="nisn" class="form-label">NISN <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="nisn" name="nisn"
                                                   placeholder="Masukkan NISN" required>
                                            <div class="invalid-feedback" data-field="nisn"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Info -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="birth_place" name="birth_place"
                                                   placeholder="Masukkan tempat lahir" required>
                                            <div class="invalid-feedback" data-field="birth_place"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control flatpickr-input" data-provider="flatpickr"
                                                   data-date-format="Y-m-d" id="birth_date" name="birth_date"
                                                   placeholder="Masukkan tanggal lahir" readonly="readonly" required>
                                            <div class="invalid-feedback" data-field="birth_date"></div>
                                        </div>
                                    </div>

                                    <!-- Religion -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="religion" class="form-label">Agama <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="religion" name="religion" required>
                                                <option value="">Pilih Agama</option>
                                                <?php $__currentLoopData = $religions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <div class="invalid-feedback" data-field="religion"></div>
                                        </div>
                                    </div>

                                    <!-- Entry Year -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="entry_year" class="form-label">Tahun Masuk <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="entry_year" name="entry_year"
                                                   min="2000" max="2100" value="<?php echo e(date('Y')); ?>" required>
                                            <div class="invalid-feedback" data-field="entry_year"></div>
                                        </div>
                                    </div>

                                    <!-- Contact Info -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="phone" class="form-label">Nomor Telepon</label>
                                            <input type="text" class="form-control" id="phone" name="phone"
                                                   placeholder="Masukkan nomor telepon">
                                            <div class="invalid-feedback" data-field="phone"></div>
                                        </div>
                                    </div>

                                    <!-- Address -->
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="address" class="form-label">Alamat <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="address" name="address" rows="3"
                                                      placeholder="Masukkan alamat lengkap" required></textarea>
                                            <div class="invalid-feedback" data-field="address"></div>
                                        </div>
                                    </div>

                                    <!-- Profile Picture -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="profile_picture" class="form-label">Foto Profil</label>
                                            <input type="file" class="form-control" id="profile_picture" name="profile_picture"
                                                   accept="image/*">
                                            <small class="text-muted">Format: JPG, PNG, JPEG. Maks: 2MB</small>
                                            <div class="invalid-feedback" data-field="profile_picture"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Parent Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Orang Tua/Wali</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="parent_name" class="form-label">Nama Orang Tua/Wali <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="parent_name" name="parent_name"
                                                   placeholder="Masukkan nama orang tua/wali" required>
                                            <div class="invalid-feedback" data-field="parent_name"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="parent_phone" class="form-label">Nomor Telepon Orang Tua/Wali <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="parent_phone" name="parent_phone"
                                                   placeholder="Masukkan nomor telepon orang tua/wali" required>
                                            <div class="invalid-feedback" data-field="parent_phone"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Akun</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Email -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   placeholder="<EMAIL>" required>
                                            <div class="invalid-feedback" data-field="email"></div>
                                        </div>
                                    </div>

                                    <!-- Password -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                            <div class="position-relative">
                                                <input type="password" class="form-control" id="password" name="password" required>
                                                <button type="button" class="position-absolute top-50 end-0 translate-middle-y btn btn-sm"
                                                        id="password-toggle" style="margin-right: 10px;">
                                                    <i class="ri-eye-off-line"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Minimal 8 karakter</small>
                                            <div class="invalid-feedback" data-field="password"></div>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($value); ?>" <?php echo e($value == 1 ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <div class="invalid-feedback" data-field="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="<?php echo e(route('admin.students.index')); ?>" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="button" class="btn btn-primary" id="btn-create-student">
                                        <i class="ri-save-line align-bottom"></i> Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        $(document).ready(function() {
            // Password toggle
            $('#password-toggle').on('click', function() {
                const passwordInput = $('#password');
                const icon = $(this).find('i');

                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    icon.removeClass('ri-eye-off-line').addClass('ri-eye-line');
                } else {
                    passwordInput.attr('type', 'password');
                    icon.removeClass('ri-eye-line').addClass('ri-eye-off-line');
                }
            });

            // Form submission
            $('#createStudentForm').on('submit', function(e) {
                e.preventDefault();

                // Reset previous validation errors
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                // Show loading state
                const submitBtn = $('#submit-btn');
                const originalBtnText = submitBtn.html();
                submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
                submitBtn.prop('disabled', true);

                // Prepare form data
                const formData = new FormData(this);

                // Submit form via AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                window.location.href = "<?php echo e(route('admin.students.index')); ?>";
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            // Validation errors
                            const errors = xhr.responseJSON.errors;

                            // Display validation errors
                            $.each(errors, function(field, messages) {
                                const inputField = $(`[name="${field}"]`);
                                inputField.addClass('is-invalid');

                                const errorField = $(`[data-field="${field}"]`);
                                errorField.text(messages[0]);
                            });
                        } else {
                            // General error
                            Swal.fire({
                                title: 'Error!',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan data',
                                icon: 'error'
                            });
                        }
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.html(originalBtnText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });

            // Initialize Flatpickr for date inputs
            flatpickr("[data-provider='flatpickr']", {
                dateFormat: "Y-m-d",
                maxDate: "today", // Prevents future dates
                disableMobile: "true",
                allowInput: true,
                locale: {
                    firstDayOfWeek: 1,
                    weekdays: {
                        shorthand: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
                        longhand: ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
                    },
                    months: {
                        shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                        longhand: ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember']
                    }
                }
            });
        });

        async function handleCreateStudent(buttonElement) {
            const form = document.getElementById('create-student-form');
            const errorMsg = document.getElementById('student-error-msg');

            if (!form || !errorMsg || !buttonElement) {
                console.error('Element not found!');
                return;
            }

            errorMsg.classList.add('d-none');

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Save button content before modification
            const originalBtnContent = buttonElement.innerHTML;
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="ri-loader-4-line align-bottom animate-spin"></i> Memproses...';

            const formData = new FormData(form);

            try {
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: result.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        window.location.href = '<?php echo e(route('admin.students.index')); ?>';
                    });
                } else {
                    throw new Error(result.message || 'Terjadi kesalahan, silakan coba lagi.');
                }
            } catch (error) {
                console.error('Error:', error);
                errorMsg.textContent = error.message;
                errorMsg.classList.remove('d-none');

                // Scroll to error message
                errorMsg.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            } finally {
                // Restore button state
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalBtnContent;
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/student/create.blade.php ENDPATH**/ ?>