@extends('admin.layouts.app')

@section('title', 'Edit Siswa')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Edit Data Siswa',
        'breadcrumb' => 'Manajemen Akun',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">
                            <i class="ri-edit-line text-muted me-1"></i> Edit Data Siswa
                        </h5>
                        <div class="flex-shrink-0">
                            <a href="{{ route('admin.students.index') }}" class="btn btn-soft-danger">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form id="editStudentForm" action="{{ route('admin.students.update', $student->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Personal Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Pribadi</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Name -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ old('name', $student->user->name) }}"
                                                   placeholder="Masukkan nama lengkap" required>
                                            <div class="invalid-feedback" data-field="name"></div>
                                        </div>
                                    </div>

                                    <!-- Gender -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                            <div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-male" value="male" required
                                                           {{ old('gender', $student->gender?->value) === 'male' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gender-male">Laki-laki</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-female" value="female" required
                                                           {{ old('gender', $student->gender?->value) === 'female' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gender-female">Perempuan</label>
                                                </div>
                                            </div>
                                            <div class="invalid-feedback" data-field="gender"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Place -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="birth_place" name="birth_place"
                                                   value="{{ old('birth_place', $student->birth_place) }}"
                                                   placeholder="Masukkan tempat lahir" required>
                                            <div class="invalid-feedback" data-field="birth_place"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Date -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control flatpickr-input" data-provider="flatpickr"
                                                   data-date-format="Y-m-d" id="birth_date" name="birth_date"
                                                   value="{{ old('birth_date', $student->birth_date?->format('Y-m-d')) }}"
                                                   placeholder="Masukkan tanggal lahir" readonly="readonly" required>
                                            <div class="invalid-feedback" data-field="birth_date"></div>
                                        </div>
                                    </div>

                                    <!-- Student ID Numbers -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="nis" class="form-label">NIS <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="nis" name="nis"
                                                   value="{{ old('nis', $student->nis) }}"
                                                   placeholder="Masukkan NIS" required>
                                            <div class="invalid-feedback" data-field="nis"></div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="nisn" class="form-label">NISN <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="nisn" name="nisn"
                                                   value="{{ old('nisn', $student->nisn) }}"
                                                   placeholder="Masukkan NISN" required>
                                            <div class="invalid-feedback" data-field="nisn"></div>
                                        </div>
                                    </div>

                                    <!-- Entry Year -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="entry_year" class="form-label">Tahun Masuk <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="entry_year" name="entry_year"
                                                   value="{{ old('entry_year', $student->entry_year) }}"
                                                   min="2000" max="{{ date('Y') + 1 }}" required>
                                            <div class="invalid-feedback" data-field="entry_year"></div>
                                        </div>
                                    </div>

                                    <!-- Phone -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="phone_number" class="form-label">Nomor Telepon</label>
                                            <input type="text" class="form-control" id="phone_number" name="phone_number"
                                                   value="{{ old('phone_number', $student->phone_number) }}"
                                                   placeholder="Masukkan nomor telepon">
                                            <div class="invalid-feedback" data-field="phone_number"></div>
                                        </div>
                                    </div>

                                    <!-- Address -->
                                    <div class="col-lg-12">
                                        <div class="form-group">
                                            <label for="address" class="form-label">Alamat</label>
                                            <textarea class="form-control" id="address" name="address" rows="3"
                                                      placeholder="Masukkan alamat lengkap">{{ old('address', $student->address) }}</textarea>
                                            <div class="invalid-feedback" data-field="address"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Parent Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Orang Tua/Wali</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="parent_name" class="form-label">Nama Orang Tua/Wali <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="parent_name" name="parent_name"
                                                   value="{{ old('parent_name', $student->parent_name) }}"
                                                   placeholder="Masukkan nama orang tua/wali" required>
                                            <div class="invalid-feedback" data-field="parent_name"></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="parent_phone" class="form-label">Nomor Telepon Orang Tua/Wali <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="parent_phone" name="parent_phone"
                                                   value="{{ old('parent_phone', $student->parent_phone) }}"
                                                   placeholder="Masukkan nomor telepon orang tua/wali" required>
                                            <div class="invalid-feedback" data-field="parent_phone"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Akun</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Email -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="{{ old('email', $student->user->email) }}"
                                                   placeholder="<EMAIL>" required>
                                            <div class="invalid-feedback" data-field="email"></div>
                                        </div>
                                    </div>

                                    <!-- Password -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="password" class="form-label">Password</label>
                                            <div class="position-relative">
                                                <input type="password" class="form-control" id="password" name="password">
                                                <button type="button" class="position-absolute top-50 end-0 translate-middle-y btn btn-sm"
                                                        id="password-toggle" style="margin-right: 10px;">
                                                    <i class="ri-eye-off-line"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Kosongkan jika tidak ingin mengubah password. Minimal 8 karakter.</small>
                                            <div class="invalid-feedback" data-field="password"></div>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                @foreach ($statuses as $value => $label)
                                                    <option value="{{ $value }}" {{ old('status', $student->user->status->value) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" data-field="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="{{ route('admin.students.index') }}" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan Perubahan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <!-- Sweet Alert css-->
    <link href="{{ asset('assets/libs/sweetalert2/sweetalert2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/libs/flatpickr/flatpickr.min.css') }}" rel="stylesheet" type="text/css" />
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
                    // Toggle password visibility
                    $('#password-toggle').on('click', function() {
                        const passwordInput = $('#password');
                        const icon = $(this).find('i');

                        if (passwordInput.attr('type') === 'password') {
                            passwordInput.attr('type', 'text');
                            icon.removeClass('ri-eye-off-line').addClass('ri-eye-line');
                        } else {
                            passwordInput.attr('type', 'password');
                            icon.removeClass('ri-eye-line').addClass('ri-eye-off-line');
                        }
                    });

                    flatpickr("[data-provider='flatpickr']", {
                        dateFormat: "Y-m-d",
                        maxDate: "today", // Prevents future dates
                        disableMobile: "true",
                        allowInput: true,
                        locale: {
                            firstDayOfWeek: 1,
                            weekdays: {
                                shorthand: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
                                longhand: ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
                            },
                            months: {
                                shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                                longhand: ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember']
                            }
                        }
                    });

                    // Form submission
                    $('#editStudentForm').on('submit', function(e) {
                        e.preventDefault();

                        // Reset previous validation errors
                        $('.is-invalid').removeClass('is-invalid');
                        $('.invalid-feedback').text('');

                        // Show loading state
                        const submitBtn = $('#submit-btn');
                        const originalBtnText = submitBtn.html();
                        submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
                        submitBtn.prop('disabled', true);

                        // Prepare form data
                        const formData = new FormData(this);

                        // Submit form via AJAX
                        $.ajax({
                            url: $(this).attr('action'),
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire({
                                        title: 'Berhasil!',
                                        text: response.message,
                                        icon: 'success',
                                        showConfirmButton: false,
                                        timer: 1500
                                    }).then(() => {
                                        window.location.href = "{{ route('admin.students.index') }}";
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Gagal',
                                        text: response.message,
                                        icon: 'error'
                                    });
                                }
                            },
                            error: function(xhr) {
                                if (xhr.status === 422) {
                                    // Validation errors
                                    const errors = xhr.responseJSON.errors;

                                    // Display validation errors
                                    $.each(errors, function(field, messages) {
                                        const inputField = $(`[name="${field}"]`);
                                        inputField.addClass('is-invalid');

                                        const errorField = $(`[data-field="${field}"]`);
                                        errorField.text(messages[0]);
                                    });
                                } else {
                                    // General error
                                    Swal.fire({
                                        title: 'Error!',
                                        text: xhr.responseJSON?.message || 'Terjadi kesalahan saat memperbarui data',
                                        icon: 'error'
                                    });
                                }
                            },
                            complete: function() {
                                // Restore button state
                                submitBtn.html(originalBtnText);
                                submitBtn.prop('disabled', false);
                            }
                        });
                    });
    </script>
@endpush

const result = await response.json();

if (result.success) {
Swal.fire({
icon: 'success',
title: 'Berhasil',
text: result.message,
showConfirmButton: false,
timer: 1500
}).then(() => {
window.location.href = '{{ route('admin.students.index') }}';
});
} else {
throw new Error(result.message || 'Terjadi kesalahan, silakan coba lagi.');
}
} catch (error) {
console.error('Error:', error);
errorMsg.textContent = error.message;
errorMsg.classList.remove('d-none');

// Scroll to error message
errorMsg.scrollIntoView({
behavior: 'smooth',
block: 'center'
});
} finally {
// Restore button state
buttonElement.disabled = false;
buttonElement.innerHTML = originalBtnContent;
}
}
</script>
@endpush
