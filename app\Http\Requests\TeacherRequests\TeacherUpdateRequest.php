<?php

namespace App\Http\Requests\TeacherRequests;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class TeacherUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $teacherId = $this->route('teacher');

        return [
            // User data
            'name' => ['sometimes', 'string', 'max:255'],
            'username' => ['sometimes', 'string', 'max:100', Rule::unique('users', 'username')->ignore($this->getUserIdForTeacher($teacherId))],
            'email' => ['sometimes', 'string', 'email', 'max:255', Rule::unique('users', 'email')->ignore($this->getUserIdForTeacher($teacherId))],
            'password' => ['sometimes', 'nullable', 'string', Password::defaults()],
            'role' => [
                'sometimes',
                'string',
                Rule::in([
                    RoleEnum::SUBJECT_TEACHER->value,
                    RoleEnum::SUBSTITUTE_TEACHER->value,
                ])
            ],
            'status' => ['sometimes', 'integer', Rule::in(UserStatus::values())],

            // Teacher profile data
            'birth_place' => ['sometimes', 'string', 'max:100'],
            'birth_date' => ['sometimes', 'date', 'before:today'],
            'gender' => ['sometimes', Rule::enum(GenderEnum::class)],
            'phone_number' => ['sometimes', 'nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
            'full_address' => ['sometimes', 'nullable', 'string', 'max:500'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes()
    {
        return [
            'name' => 'Nama',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'role' => 'Role',
            'status' => 'Status',
            'birth_place' => 'Tempat lahir',
            'birth_date' => 'Tanggal lahir',
            'gender' => 'Jenis kelamin',
            'phone_number' => 'Nomor telepon',
            'full_address' => 'Alamat lengkap',
        ];
    }

    /**
     * Get user ID for the teacher through service layer
     */
    private function getUserIdForTeacher(int $teacherId): ?int
    {
        try {
            $teacherService = app(\App\Services\TeacherService::class);
            $teacher = $teacherService->findById($teacherId);
            return $teacher->user_id;
        } catch (\Exception $e) {
            return null;
        }
    }
}
