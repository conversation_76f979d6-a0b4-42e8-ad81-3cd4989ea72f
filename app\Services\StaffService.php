<?php

namespace App\Services;

use App\Repositories\Contracts\StaffRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\StaffException; // Dipertahankan
use App\Models\User; // Staff adalah User
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth; // Untuk validasi user saat ini
use Illuminate\Database\Eloquent\ModelNotFoundException; // Untuk menangkap dari repository
use Throwable;

class StaffService
{
    protected const MAX_ADMIN_COUNT = 5; // Batasan yang sudah kita tetapkan

    public function __construct(
        protected StaffRepositoryInterface $staffRepository,
        // protected UserRepositoryInterface $userRepository, // Tidak perlu lagi jika menggunakan UserService
        protected UserService $userService // Injeksikan UserService
    ) {
        // Karena StaffRepository sekarang mengandalkan UserRepository yang lebih tipis,
        // kita bisa langsung memanggil UserService di sini
    }

    /**
     * Get all staff with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->staffRepository->getAll($filters);
    }

    /**
     * Get all active staff.
     */
    public function getAllActiveStaff(): Collection
    {
        return $this->staffRepository->getAllActive();
    }

    /**
     * Find a staff member by ID.
     * Converts ModelNotFoundException to BusinessLogicException for consistency.
     */
    public function findById(int $id): User
    {
        try {
            // Panggil findById dari StaffRepository
            $staff = $this->staffRepository->findById($id);

            // Periksa kembali apakah user yang ditemukan memang staff (jika StaffRepository belum memvalidasi ini)
            // StaffRepository->findById() sudah memvalidasi ini.
            return $staff;
        } catch (ModelNotFoundException $e) {
            throw new BusinessLogicException("Staf dengan ID {$id} tidak ditemukan.");
        }
    }

    /**
     * Create a new staff member.
     * Menggunakan UserService untuk membuat User, lalu validasi peran staff.
     */
    public function create(array $data): User
    {
        return DB::transaction(function () use ($data) {
            try {
                // Validasi peran staf dan batasan jumlah admin/principal/treasurer
                $this->validateStaffRoleConstraints($data['role'], null);

                // Buat user melalui UserService, yang sudah menangani hashing password dan penugasan peran
                $user = $this->userService->create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => $data['password'],
                    'status' => $data['status'] ?? UserStatus::Active->value,
                    'role' => $data['role'],
                    'username' => $data['username'] ?? strtolower(explode('@', $data['email'])[0]),
                ]);

                return $user->load('roles'); // Muat ulang role untuk memastikan terload
            } catch (BusinessLogicException | StaffException $e) {
                throw $e; // Lempar ulang pengecualian bisnis yang sudah spesifik
            } catch (Throwable $e) {
                // Tangkap pengecualian umum lainnya sebagai BusinessLogicException
                throw new BusinessLogicException('Gagal membuat data staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Update staff information.
     * Menggunakan UserService untuk memperbarui User dan memvalidasi peran.
     */
    public function update(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            try {
                $staff = $this->findById($id); // Memastikan staf ada (melempar BusinessLogicException jika tidak)

                // Jika peran diubah, validasi batasan
                if (isset($data['role'])) {
                    $this->validateStaffRoleConstraints($data['role'], $staff->id);
                }

                // Perbarui user melalui UserService
                // UserService sudah menangani hashing password, role sync, dan status update
                return $this->userService->update($id, $data);
            } catch (BusinessLogicException | StaffException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal memperbarui data staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Delete a staff member.
     */
    public function delete(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            try {
                $staff = $this->findById($id); // Memastikan staf ada

                // Validasi bisnis untuk penghapusan staf
                $this->validateStaffDeletion($staff); // Menerima objek User/Staff

                // Hapus user melalui UserService
                return $this->userService->delete($id);
            } catch (BusinessLogicException | StaffException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal menghapus data staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Change staff member status.
     * Menggunakan UserService untuk mengelola status user.
     */
    public function changeStatus(int $id, bool $status): User
    {
        return DB::transaction(function () use ($id, $status) {
            try {
                $staff = $this->findById($id); // Memastikan staf ada

                // Validasi bisnis: tidak dapat mengubah status akun sendiri
                if ($staff->id === Auth::id()) {
                    throw new BusinessLogicException('Tidak dapat mengubah status akun sendiri.');
                }

                // Gunakan UserService untuk mengubah status
                $this->userService->changeStatus($id, $status);

                return $this->findById($id); // Mengembalikan objek user yang sudah diperbarui
            } catch (BusinessLogicException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal mengubah status staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Validate staff role constraints (e.g., single principal, max admins).
     * Ini adalah logika bisnis utama untuk penugasan peran staf.
     *
     * @throws StaffException|BusinessLogicException
     */
    protected function validateStaffRoleConstraints(string $role, ?int $excludeUserId = null): void
    {
        // Pengecekan apakah peran diizinkan untuk staf
        if (!in_array($role, $this->staffRepository->getAllowedRoles())) {
            throw StaffException::invalidRole($role);
        }

        // Pengecekan Principal (hanya satu Principal)
        if ($role === RoleEnum::PRINCIPAL->value) {
            $existingPrincipalCount = $this->staffRepository->countByRole(RoleEnum::PRINCIPAL->value);
            // Jika ada principal lain yang bukan user yang sedang diupdate
            if ($existingPrincipalCount > 0 && ($excludeUserId === null || $existingPrincipalCount > ($excludeUserId ? 1 : 0))) {
                throw StaffException::principalAlreadyExists();
            }
        }

        // Pengecekan Treasurer (hanya satu Treasurer)
        if ($role === RoleEnum::TREASURER->value) {
            $existingTreasurerCount = $this->staffRepository->countByRole(RoleEnum::TREASURER->value);
            // Jika ada treasurer lain yang bukan user yang sedang diupdate
            if ($existingTreasurerCount > 0 && ($excludeUserId === null || $existingTreasurerCount > ($excludeUserId ? 1 : 0))) {
                throw StaffException::treasurerAlreadyExists();
            }
        }

        // Pengecekan Admin (maksimal 5 admin)
        if ($role === RoleEnum::ADMIN->value) {
            $adminCount = $this->staffRepository->countByRole(RoleEnum::ADMIN->value);
            // Jika sedang membuat atau jika update akan menambah admin melebihi batas (dan user bukan admin sebelumnya)
            if ($adminCount >= self::MAX_ADMIN_COUNT && ($excludeUserId === null || ($adminCount >= self::MAX_ADMIN_COUNT && !$this->userRepository->findById($excludeUserId)->hasRole(RoleEnum::ADMIN->value)))) {
                // Periksa juga apakah user yang di-update BUKAN admin sebelumnya
                if ($excludeUserId && $this->userRepository->findById($excludeUserId)->hasRole(RoleEnum::ADMIN->value)) {
                    // Jika user yang di-update sudah admin, tidak masalah
                } else {
                    // Jika user yang di-update bukan admin, dan batas tercapai
                    throw StaffException::maxAdminsReached(self::MAX_ADMIN_COUNT);
                }
            }
        }
    }

    /**
     * Validate if a staff member can be deleted.
     *
     * @throws StaffException|BusinessLogicException
     */
    protected function validateStaffDeletion(User $staff): void
    {
        // Tidak dapat menghapus diri sendiri
        if ($staff->id === Auth::id()) {
            throw new BusinessLogicException('Tidak dapat menghapus akun sendiri.');
        }

        // Tidak dapat menghapus jika statusnya aktif
        if ($staff->status === UserStatus::Active) {
            throw StaffException::cannotDeleteActiveStaff();
        }

        // Tidak dapat menghapus admin terakhir
        if ($staff->hasRole(RoleEnum::ADMIN->value)) {
            $activeAdminCount = $this->staffRepository->countByRoleAndStatus(RoleEnum::ADMIN->value, UserStatus::Active->value); // Perlu method baru di repo
            if ($activeAdminCount <= 1) {
                throw StaffException::cannotDeleteLastAdmin();
            }
        }
        // Tambahkan validasi bisnis lain jika diperlukan (misal: apakah staf memiliki tugas aktif dll.)
    }
}
