<?php $__env->startSection('title', 'Edit Guru'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Edit Guru',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <div>
                            <h5 class="card-title mb-0">Form Data Guru</h5>
                        </div>
                        <div>
                            <a href="<?php echo e(route('admin.teachers.index')); ?>" class="btn btn-ghost-info">
                                <i class="ri-arrow-left-line align-bottom"></i> <PERSON><PERSON><PERSON>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="editTeacherForm" action="<?php echo e(route('admin.teachers.update', $teacher->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Personal Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Pribadi</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Gender -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                            <div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-male" value="male" required
                                                           <?php echo e(old('gender', $teacher->gender) === 'male' ? 'checked' : ''); ?>>
                                                    <label class="form-check-label" for="gender-male">Laki-laki</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender"
                                                           id="gender-female" value="female" required
                                                           <?php echo e(old('gender', $teacher->gender) === 'female' ? 'checked' : ''); ?>>
                                                    <label class="form-check-label" for="gender-female">Perempuan</label>
                                                </div>
                                            </div>
                                            <div class="invalid-feedback" data-field="gender"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Place -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="birth_place" name="birth_place"
                                                   value="<?php echo e(old('birth_place', $teacher->birth_place)); ?>"
                                                   placeholder="Masukkan tempat lahir" required>
                                            <div class="invalid-feedback" data-field="birth_place"></div>
                                        </div>
                                    </div>

                                    <!-- Birth Date -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date"
                                                   value="<?php echo e(old('birth_date', $teacher->birth_date)); ?>"
                                                   placeholder="Masukkan tanggal lahir" required>
                                            <div class="invalid-feedback" data-field="birth_date"></div>
                                        </div>
                                    </div>

                                    <!-- Phone -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="phone_number" class="form-label">Nomor Telepon</label>
                                            <input type="text" class="form-control" id="phone_number" name="phone_number"
                                                   value="<?php echo e(old('phone_number', $teacher->phone_number)); ?>"
                                                   placeholder="Masukkan nomor telepon">
                                            <div class="invalid-feedback" data-field="phone_number"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="card shadow-none border mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">Informasi Akun</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Name -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="<?php echo e(old('name', $teacher->user->name)); ?>"
                                                   placeholder="Masukkan nama lengkap" required>
                                            <div class="invalid-feedback" data-field="name"></div>
                                        </div>
                                    </div>

                                    <!-- Email -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="<?php echo e(old('email', $teacher->user->email)); ?>"
                                                   placeholder="<EMAIL>" required>
                                            <div class="invalid-feedback" data-field="email"></div>
                                        </div>
                                    </div>

                                    <!-- Username -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="username" name="username"
                                                   value="<?php echo e(old('username', $teacher->user->username)); ?>"
                                                   placeholder="Masukkan username" required>
                                            <div class="invalid-feedback" data-field="username"></div>
                                        </div>
                                    </div>

                                    <!-- Password -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="password" class="form-label">Password</label>
                                            <div class="position-relative">
                                                <input type="password" class="form-control" id="password" name="password">
                                                <button type="button" class="position-absolute top-50 end-0 translate-middle-y btn btn-sm"
                                                        id="password-toggle" style="margin-right: 10px;">
                                                    <i class="ri-eye-off-line"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Kosongkan jika tidak ingin mengubah password. Minimal 8 karakter.</small>
                                            <div class="invalid-feedback" data-field="password"></div>
                                        </div>
                                    </div>

                                    <!-- Role -->
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                            <select class="form-select" data-choices id="role" name="role" required>
                                                <option value="">Pilih Role</option>
                                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(str_contains($value, 'teacher')): ?>
                                                        <option value="<?php echo e($value); ?>" <?php echo e(old('role', $currentRole) === $value ? 'selected' : ''); ?>>
                                                            <?php echo e($label); ?>

                                                        </option>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <div class="invalid-feedback" data-field="role"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="ri-save-line align-bottom me-1"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            flatpickr("#birth_date", {
                dateFormat: "Y-m-d",
                allowInput: true,
                maxDate: "today",
            });

            $('#password-toggle').on('click', function() {
                const passwordInput = $('#password');
                const icon = $(this).find('i');

                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    icon.removeClass('ri-eye-off-line').addClass('ri-eye-line');
                } else {
                    passwordInput.attr('type', 'password');
                    icon.removeClass('ri-eye-line').addClass('ri-eye-off-line');
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/teacher/edit.blade.php ENDPATH**/ ?>