<?php $__env->startSection('title', 'Tahun Akademik'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Tahun Akademik',
        'breadcrumb' => 'Manajemen Akademik',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar <?php echo $__env->yieldContent('title'); ?>
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-academic-years">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <a href="<?php echo e(route('admin.academic-years.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status-filter" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="status-filter">
                                    <option value="">Semua Status</option>
                                    <option value="planned">Direncanakan</option>
                                    <option value="active">Aktif</option>
                                    <option value="completed">Selesai</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="semester-filter" class="form-label">Semester</label>
                                <select class="form-select" data-choices name="semester" id="semester-filter">
                                    <option value="">Semua Semester</option>
                                    <option value="Ganjil">Ganjil</option>
                                    <option value="Genap">Genap</option>
                                </select>
                            </div>
                        </div>
                        <!-- Search Input -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari tahun akademik..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable" class="table nowrap align-middle table-striped" style="width:100%">
                            <thead class="table-light">
                                <tr>
                                    <th>No</th>
                                    <th>Tahun Akademik</th>
                                    <th>Semester</th>
                                    <th>Tanggal Mulai</th>
                                    <th>Tanggal Selesai</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <!-- DataTables CSS -->
    <link href="<?php echo e(asset('assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css')); ?>" rel="stylesheet" type="text/css" />
    <style>
        .dataTables_filter {
            display: none;
        }

        .dataTables_length {
            padding-bottom: 15px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.datatables', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        let table;
        $(document).ready(function() {
            // Initialize DataTable with enhanced configuration
            table = $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "<?php echo e(route('admin.academic-years.index')); ?>",
                    data: function(d) {
                        return {
                            ...d,
                            status: $('#status-filter').val(),
                            semester: $('#semester-filter').val(),
                            search: $('#search-input').val()
                        };
                    },
                    complete: function(response) {
                        $('#total-academic-years').text(response.responseJSON.recordsTotal || 0);
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'semester_label',
                        name: 'semester',
                        orderable: true
                    },
                    {
                        data: 'start_date_formatted',
                        name: 'start_date'
                    },
                    {
                        data: 'end_date_formatted',
                        name: 'end_date'
                    },
                    {
                        data: 'status_label',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                },
                order: [
                    [1, 'asc']
                ], // Sort by name
                columnDefs: [{
                        orderable: false,
                        targets: [0, 6]
                    },
                    {
                        className: "text-center",
                        targets: [0]
                    }
                ]
            });

            // Custom search box
            $('#search-button').on('click', function() {
                table.draw();
            });

            $('#search-input').on('keyup', function(e) {
                if (e.key === 'Enter') {
                    table.draw();
                }
            });

            // Status filter
            $('#status-filter').on('change', function() {
                table.draw();
            });

            // Semester filter
            $('#semester-filter').on('change', function() {
                table.draw();
            });

            // Export button
            $('#export-btn').on('click', function() {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur export akan segera tersedia.',
                    icon: 'info'
                });
            });

            // Delete functionality
            $(document).on('click', '.delete-btn', function() {
                const academicYearId = $(this).data('id');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: "Data tahun akademik akan dihapus dan tidak dapat dikembalikan!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `<?php echo e(url('admin/academic-years')); ?>/${academicYearId}`,
                            type: 'DELETE',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(() => {
                                    window.location.reload();
                                });
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    title: 'Error!',
                                    text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.',
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/academic-year/index.blade.php ENDPATH**/ ?>