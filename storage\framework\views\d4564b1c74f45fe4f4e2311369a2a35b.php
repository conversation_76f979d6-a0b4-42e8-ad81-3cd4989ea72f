<?php $__env->startSection('title', 'Edit Siswa'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Edit Data Siswa',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">
                            <i class="ri-edit-line text-muted me-1"></i> Edit Data Siswa
                        </h5>
                        <div class="flex-shrink-0">
                            <a href="<?php echo e(route('admin.students.index')); ?>" class="btn btn-soft-danger">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div id="student-error-msg" class="alert alert-danger py-2 d-none"></div>

                    <form id="edit-student-form" action="<?php echo e(route('admin.students.update', $student->id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row g-3">
                            <div class="col-12">
                                <!-- Nav tabs -->
                                <ul class="nav nav-tabs" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#profile-info" role="tab">
                                            <i class="ri-profile-line me-1"></i> Informasi Siswa
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#account-settings" role="tab">
                                            <i class="ri-user-settings-line me-1"></i> Pengaturan Akun
                                        </a>
                                    </li>
                                </ul>

                                <!-- Tab panes -->
                                <div class="tab-content p-3 border border-top-0 rounded-bottom">
                                    <!-- Profile Information Tab -->
                                    <div class="tab-pane active" id="profile-info" role="tabpanel">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="nis" class="form-label">NIS <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="nis" name="nis" value="<?php echo e($student->nis); ?>" required>
                                                <div class="invalid-feedback" data-field="nis"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="nisn" class="form-label">NISN</label>
                                                <input type="text" class="form-control" id="nisn" name="nisn" value="<?php echo e($student->nisn); ?>">
                                                <div class="invalid-feedback" data-field="nisn"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="birth_place" class="form-label">Tempat Lahir <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="birth_place" name="birth_place" value="<?php echo e($student->birth_place); ?>" required>
                                                <div class="invalid-feedback" data-field="birth_place"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control flatpickr-input" data-provider="flatpickr"
                                                       data-date-format="Y-m-d" id="birth_date" name="birth_date"
                                                       value="<?php echo e($student->birth_date); ?>" required>
                                                <div class="invalid-feedback" data-field="birth_date"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                                <select class="form-select" data-choices id="gender" name="gender" required>
                                                    <option value="">Pilih Jenis Kelamin</option>
                                                    <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($value); ?>" <?php echo e($student->gender === $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <div class="invalid-feedback" data-field="gender"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Agama</label>
                                                <select class="form-select" data-choices id="religion" name="religion">
                                                    <option value="">Pilih Agama</option>
                                                    <?php $__currentLoopData = $religions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($value); ?>" <?php echo e($student->religion === $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <div class="invalid-feedback" data-field="religion"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="phone_number" class="form-label">Nomor Telepon</label>
                                                <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo e($student->phone_number); ?>">
                                                <div class="invalid-feedback" data-field="phone_number"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="profile_picture" class="form-label">Foto Profil</label>
                                                <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                                                <div class="invalid-feedback" data-field="profile_picture"></div>
                                                <?php if($student->profile_picture): ?>
                                                    <div class="mt-2">
                                                        <img src="<?php echo e(asset('storage/' . $student->profile_picture)); ?>" alt="Profile Picture" class="avatar-md">
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-12">
                                                <label for="full_address" class="form-label">Alamat Lengkap</label>
                                                <textarea class="form-control" id="full_address" name="full_address" rows="3"><?php echo e($student->full_address); ?></textarea>
                                                <div class="invalid-feedback" data-field="full_address"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Account Settings Tab -->
                                    <div class="tab-pane" id="account-settings" role="tabpanel">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="name" name="name"
                                                       value="<?php echo e($student->user->name); ?>" required>
                                                <div class="invalid-feedback" data-field="name"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                                <input type="email" class="form-control" id="email" name="email"
                                                       value="<?php echo e($student->user->email); ?>" required>
                                                <div class="invalid-feedback" data-field="email"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="account_password" class="form-label">Password <small class="text-muted">(Opsional)</small></label>
                                                <div class="position-relative">
                                                    <input type="password" class="form-control" id="account_password" name="password">
                                                    <button type="button" class="position-absolute top-50 end-0 translate-middle-y btn btn-sm"
                                                            id="account-password-toggle" style="margin-right: 10px;">
                                                        <i class="ri-eye-off-line"></i>
                                                    </button>
                                                </div>
                                                <div class="invalid-feedback" data-field="password"></div>
                                                <small class="text-muted">Kosongkan jika tidak ingin mengubah password</small>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                <select class="form-select" data-choices id="status" name="status" required>
                                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($value); ?>" <?php echo e($student->user->status == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <div class="invalid-feedback" data-field="status"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="<?php echo e(route('admin.students.index')); ?>" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="button" class="btn btn-primary" id="btn-update-student">
                                        <i class="ri-save-line align-bottom"></i> Simpan Perubahan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <!-- Sweet Alert css-->
    <link href="<?php echo e(asset('assets/libs/sweetalert2/sweetalert2.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/flatpickr/flatpickr.min.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- Sweet Alerts js -->
    <script src="<?php echo e(asset('assets/libs/sweetalert2/sweetalert2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/flatpickr/flatpickr.min.js')); ?>"></script>

    <script type="text/javascript">
        $(document).ready(function() {
            // Initialize Flatpickr
            flatpickr("[data-provider='flatpickr']", {
                dateFormat: "Y-m-d",
                maxDate: "today",
                disableMobile: "true",
                allowInput: true,
                locale: {
                    firstDayOfWeek: 1,
                    weekdays: {
                        shorthand: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
                        longhand: ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
                    },
                    months: {
                        shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                        longhand: ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember']
                    }
                }
            });

            // Password toggle for account settings
            $('#account-password-toggle').on('click', function() {
                const passwordInput = $('#account_password');
                const icon = $(this).find('i');

                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    icon.removeClass('ri-eye-off-line').addClass('ri-eye-line');
                } else {
                    passwordInput.attr('type', 'password');
                    icon.removeClass('ri-eye-line').addClass('ri-eye-off-line');
                }
            });

            // Update student handler
            $('#btn-update-student').on('click', function() {
                handleUpdateStudent(this);
            });
        });

        async function handleUpdateStudent(buttonElement) {
            const form = document.getElementById('edit-student-form');
            const errorMsg = document.getElementById('student-error-msg');

            if (!form || !errorMsg || !buttonElement) {
                console.error('Element not found!');
                return;
            }

            errorMsg.classList.add('d-none');

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Save button content before modification
            const originalBtnContent = buttonElement.innerHTML;
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="ri-loader-4-line align-bottom animate-spin"></i> Memproses...';

            const formData = new FormData(form);

            try {
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: result.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        window.location.href = '<?php echo e(route('admin.students.index')); ?>';
                    });
                } else {
                    throw new Error(result.message || 'Terjadi kesalahan, silakan coba lagi.');
                }
            } catch (error) {
                console.error('Error:', error);
                errorMsg.textContent = error.message;
                errorMsg.classList.remove('d-none');

                // Scroll to error message
                errorMsg.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            } finally {
                // Restore button state
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalBtnContent;
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/student/edit.blade.php ENDPATH**/ ?>